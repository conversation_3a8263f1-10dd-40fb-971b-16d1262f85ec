# RajaMenu Auto-Render Guide

## 🚀 Cara Menampilkan Nav RajaMenu Tanpa Render Hook Manual

RajaMenuPlugin sekarang mendukung **auto-render** yang memungkinkan Anda menampilkan navigasi flyout tanpa perlu menambahkan render hook manual di PanelProvider.

## ✅ Cara Penggunaan (Recommended)

### 1. Mode Auto dengan Auto-Render (Default)

```php
// Di AdminPanelProvider.php
->plugins([
    RajaMenuPlugin::make()
        ->enabled(true)
        ->auto()
        ->panel('admin'),
    // autoRender(true) adalah default - navigasi muncul otomatis
])
```

**Keuntungan:**
- ✅ Tidak perlu render hook manual
- ✅ Plugin menangani semua rendering
- ✅ Lebih bersih dan mudah maintenance
- ✅ Konsisten di semua panel

### 2. Mode Manual dengan Auto-Render

```php
->plugins([
    RajaMenuPlugin::make()
        ->enabled(true)
        ->manual()
        ->panel('admin')
        ->dashboard()
        ->contentManagement()
        ->userManagement()
        ->systemSettings(),
    // autoRender(true) adalah default
])
```

## 🔧 Opsi Lanjutan

### Menonaktifkan Auto-Render (Jika Perlu Kontrol Manual)

```php
// Opsi 1: Menggunakan autoRender(false)
->plugins([
    RajaMenuPlugin::make()
        ->enabled(true)
        ->auto()
        ->panel('admin')
        ->autoRender(false), // Nonaktifkan auto-render
])

// Opsi 2: Menggunakan manualRender() (shortcut)
->plugins([
    RajaMenuPlugin::make()
        ->enabled(true)
        ->auto()
        ->panel('admin')
        ->manualRender(), // Sama dengan ->autoRender(false)
])

// Kemudian tambahkan render hook manual:
->renderHook(
    'panels::page.header.actions.before',
    fn() => view('rajamenu::navigation.auto-flyout')->render()
)
```

## 📋 Perbandingan Cara Lama vs Baru

### ❌ Cara Lama (Tidak Disarankan)

```php
// Di PanelProvider
->plugins([
    RajaMenuPlugin::make()
        ->enabled(true)
        ->auto()
        ->panel('admin'),
])
->renderHook(
    'panels::page.header.actions.before',
    fn() => view('rajamenu::navigation.auto-flyout')->render()
)
```

### ✅ Cara Baru (Recommended)

```php
// Di PanelProvider
->plugins([
    RajaMenuPlugin::make()
        ->enabled(true)
        ->auto()
        ->panel('admin'),
    // Selesai! Navigasi muncul otomatis
])
```

## 🎯 Konfigurasi Per Panel

### Admin Panel
```php
RajaMenuPlugin::make()
    ->enabled(true)
    ->auto()
    ->panel('admin')
```

### Member Panel
```php
RajaMenuPlugin::make()
    ->enabled(true)
    ->manual()
    ->panel('member')
    ->dashboard()
    ->addGroup('Profile', [...])
```

### Marketplace Panel
```php
RajaMenuPlugin::make()
    ->enabled(true)
    ->auto()
    ->panel('marketplace')
```

## 🔍 Troubleshooting

### Navigasi Tidak Muncul?

1. **Pastikan plugin diaktifkan:**
   ```php
   ->enabled(true)
   ```

2. **Pastikan auto-render aktif (default):**
   ```php
   // Tidak perlu ditambahkan, sudah default
   ->autoRender(true)
   ```

3. **Periksa panel ID:**
   ```php
   ->panel('admin') // Harus sesuai dengan panel ID
   ```

### Navigasi Muncul Duplikat?

Hapus render hook manual jika ada:
```php
// Hapus ini jika menggunakan auto-render
->renderHook(
    'panels::page.header.actions.before',
    fn() => view('rajamenu::navigation.auto-flyout')->render()
)
```

## 📚 API Reference

| Method | Parameter | Default | Deskripsi |
|--------|-----------|---------|-----------|
| `enabled()` | `bool $enabled` | `true` | Aktifkan/nonaktifkan plugin |
| `autoRender()` | `bool $autoRender` | `true` | Aktifkan/nonaktifkan auto-render |
| `manualRender()` | - | - | Shortcut untuk `autoRender(false)` |
| `auto()` | - | - | Set mode ke 'auto' |
| `manual()` | `array $items` | `[]` | Set mode ke 'manual' |
| `panel()` | `string $panelId` | `'admin'` | Set panel ID |

## 🎉 Kesimpulan

Dengan fitur auto-render, Anda cukup menambahkan plugin ke panel tanpa perlu render hook manual. Plugin akan otomatis menampilkan navigasi flyout di posisi yang tepat.

**Recommended setup:**
```php
->plugins([
    RajaMenuPlugin::make()
        ->enabled(true)
        ->auto()
        ->panel('admin'),
])
```

Selesai! 🚀
