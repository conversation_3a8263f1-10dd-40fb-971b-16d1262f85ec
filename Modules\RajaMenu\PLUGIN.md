# RajaMenu Plugin Documentation

Plugin FilamentPHP untuk mengaktifkan dan mengkonfigurasi RajaMenu dengan mudah di setiap panel.

## 🚀 Quick Start

### 1. Import Plugin

```php
use Modules\RajaMenu\Plugins\RajaMenuPlugin;
```

### 2. Tambahkan ke Panel

```php
public function panel(Panel $panel): Panel
{
    return $panel
        ->id('admin')
        ->path('admin')
        // ... konfigurasi panel lainnya
        ->plugins([
            RajaMenuPlugin::make()
                ->enabled(true)
                ->auto()
                ->panel('admin'),
        ]);
}
```

## 📋 API Methods

### Basic Configuration

#### `enabled(bool $enabled = true)`
Mengaktifkan atau menonaktifkan RajaMenu untuk panel ini.

```php
RajaMenuPlugin::make()
    ->enabled(true)  // Aktifkan
    ->enabled(false) // Nonaktifkan
```

#### `autoRender(bool $autoRender = true)`
Mengaktifkan atau menonaktifkan auto-render navigasi. <PERSON><PERSON> diakti<PERSON>kan, plugin akan otomatis menampilkan navigasi tanpa perlu render hook manual.

```php
RajaMenuPlugin::make()
    ->autoRender(true)   // Auto-render aktif (default)
    ->autoRender(false)  // Auto-render nonaktif
```

#### `manualRender()`
Shortcut untuk menonaktifkan auto-render. Berguna jika Anda ingin menggunakan render hook manual.

```php
RajaMenuPlugin::make()
    ->manualRender()  // Sama dengan ->autoRender(false)
```

#### `panel(string $panelId)`
Set panel ID untuk konfigurasi yang spesifik.

```php
RajaMenuPlugin::make()
    ->panel('admin')      // Panel admin
    ->panel('marketplace') // Panel marketplace
    ->panel('member')     // Panel member
```

### Navigation Modes

#### `auto()`
Menggunakan mode auto-discovery (mengambil dari FilamentPHP resources/pages).

```php
RajaMenuPlugin::make()
    ->auto()
```

#### `manual(array $navigationItems = [])`
Menggunakan mode manual dengan konfigurasi custom.

```php
RajaMenuPlugin::make()
    ->manual()
    // atau dengan items langsung
    ->manual([
        'Dashboard' => [
            'label' => 'Dashboard',
            'items' => [...]
        ]
    ])
```

#### `mode(string $mode)`
Set mode secara eksplisit.

```php
RajaMenuPlugin::make()
    ->mode('auto')   // Auto-discovery
    ->mode('manual') // Manual configuration
```

### Helper Methods untuk Manual Mode

#### `dashboard(array $config = [])`
Menambahkan group dashboard dengan konfigurasi default.

```php
RajaMenuPlugin::make()
    ->manual()
    ->dashboard()
    // atau dengan custom config
    ->dashboard([
        'label' => 'Custom Dashboard',
        'icon' => 'heroicon-o-home',
        'sort' => -10,
    ])
```

#### `contentManagement(array $config = [])`
Menambahkan group content management.

```php
RajaMenuPlugin::make()
    ->manual()
    ->contentManagement()
```

#### `userManagement(array $config = [])`
Menambahkan group user management.

```php
RajaMenuPlugin::make()
    ->manual()
    ->userManagement()
```

#### `systemSettings(array $config = [])`
Menambahkan group system settings.

```php
RajaMenuPlugin::make()
    ->manual()
    ->systemSettings()
```

#### `addGroup(string $groupName, array $groupConfig)`
Menambahkan group custom.

```php
RajaMenuPlugin::make()
    ->manual()
    ->addGroup('Ecommerce', [
        'label' => 'E-commerce',
        'icon' => 'heroicon-o-shopping-cart',
        'sort' => -3,
        'items' => [
            [
                'label' => 'Products',
                'icon' => 'heroicon-o-cube',
                'url' => '/admin/products',
                'sort' => 1,
                'badge' => '342',
                'type' => 'resource',
            ],
            // ... more items
        ],
    ])
```

## 🎯 Contoh Penggunaan

### Mode Auto (Auto-Discovery) - Auto Render

```php
->plugins([
    RajaMenuPlugin::make()
        ->enabled(true)
        ->auto()
        ->panel('admin'),
        // autoRender(true) adalah default, navigasi akan muncul otomatis
])
```

### Mode Auto - Manual Render

```php
->plugins([
    RajaMenuPlugin::make()
        ->enabled(true)
        ->auto()
        ->panel('admin')
        ->manualRender(), // Nonaktifkan auto-render
])

// Kemudian tambahkan render hook manual di panel:
->renderHook(
    'panels::page.header.actions.before',
    fn() => view('rajamenu::navigation.auto-flyout')->render()
)
```

### Mode Manual dengan Helper Methods

```php
->plugins([
    RajaMenuPlugin::make()
        ->enabled(true)
        ->manual()
        ->panel('admin')
        ->dashboard()
        ->contentManagement()
        ->userManagement()
        ->systemSettings(),
])
```

### Mode Manual dengan Custom Groups

```php
->plugins([
    RajaMenuPlugin::make()
        ->enabled(true)
        ->manual()
        ->panel('marketplace')
        ->addGroup('Dashboard', [
            'label' => 'Dashboard',
            'icon' => 'heroicon-o-home',
            'sort' => -10,
            'items' => [
                [
                    'label' => 'Marketplace Dashboard',
                    'icon' => 'heroicon-o-building-storefront',
                    'url' => '/marketplace',
                    'sort' => 1,
                    'type' => 'page',
                ],
            ],
        ])
        ->addGroup('Products', [
            'label' => 'Products',
            'icon' => 'heroicon-o-cube',
            'sort' => -5,
            'items' => [
                [
                    'label' => 'My Products',
                    'icon' => 'heroicon-o-squares-plus',
                    'url' => '/marketplace/products',
                    'sort' => 1,
                    'badge' => '25',
                    'type' => 'resource',
                ],
                [
                    'label' => 'Inventory',
                    'icon' => 'heroicon-o-archive-box',
                    'url' => '#',
                    'sort' => 2,
                    'type' => 'parent',
                    'children' => [
                        [
                            'label' => 'Stock Management',
                            'icon' => 'heroicon-o-squares-plus',
                            'url' => '/marketplace/inventory/stock',
                            'sort' => 1,
                            'type' => 'page',
                        ],
                    ],
                ],
            ],
        ]),
])
```

## 📁 Struktur Item

### Group Structure
```php
[
    'label' => 'Group Name',        // Nama group
    'icon' => 'heroicon-o-icon',   // Icon group (opsional)
    'sort' => -10,                 // Urutan group
    'items' => [                   // Array items dalam group
        // ... items
    ],
]
```

### Item Structure
```php
[
    'label' => 'Item Name',         // Nama item
    'icon' => 'heroicon-o-icon',   // Icon item
    'url' => '/panel/path',        // URL tujuan
    'sort' => 1,                   // Urutan dalam group
    'badge' => '10',               // Badge (opsional)
    'type' => 'resource',          // Tipe: resource/page/parent
    'children' => [                // Sub-items (opsional)
        [
            'label' => 'Sub Item',
            'icon' => 'heroicon-o-icon',
            'url' => '/panel/sub-path',
            'sort' => 1,
            'type' => 'page',
        ],
    ],
]
```

## 🔧 Per-Panel Configuration

### Admin Panel
```php
RajaMenuPlugin::make()
    ->enabled(true)
    ->auto()  // Gunakan auto-discovery untuk admin
    ->panel('admin')
```

### Marketplace Panel
```php
RajaMenuPlugin::make()
    ->enabled(true)
    ->manual()  // Gunakan manual untuk marketplace
    ->panel('marketplace')
    ->addGroup('Products', [...])
    ->addGroup('Orders', [...])
```

### Member Panel
```php
RajaMenuPlugin::make()
    ->enabled(true)
    ->manual()  // Gunakan manual untuk member
    ->panel('member')
    ->addGroup('Profile', [...])
    ->addGroup('Orders', [...])
```

## 🎨 Icon Recommendations

### Dashboard & Home
- `heroicon-o-home`
- `heroicon-o-squares-2x2`
- `heroicon-o-building-storefront`

### Content & Media
- `heroicon-o-document-text`
- `heroicon-o-newspaper`
- `heroicon-o-photo`
- `heroicon-o-folder`

### E-commerce
- `heroicon-o-shopping-cart`
- `heroicon-o-cube`
- `heroicon-o-shopping-bag`
- `heroicon-o-credit-card`

### Users & Security
- `heroicon-o-users`
- `heroicon-o-user-group`
- `heroicon-o-shield-check`
- `heroicon-o-key`

### System & Settings
- `heroicon-o-cog-6-tooth`
- `heroicon-o-wrench-screwdriver`
- `heroicon-o-chart-bar`

## 💡 Tips & Best Practices

1. **Gunakan Auto Mode** untuk panel admin yang kompleks
2. **Gunakan Manual Mode** untuk panel khusus (marketplace, member)
3. **Konsisten dengan Icons** - gunakan Heroicons
4. **Sorting yang Logis** - angka negatif untuk posisi awal
5. **Badge yang Bermakna** - tampilkan counter yang relevan
6. **URL yang Valid** - pastikan semua URL dapat diakses
7. **Nested Navigation** - maksimal 3 level untuk UX yang baik

## 🔍 Troubleshooting

### Plugin tidak muncul
- Pastikan plugin di-register di panel
- Cek apakah `enabled(true)` sudah dipanggil

### Navigation tidak sesuai
- Periksa konfigurasi `panel()` ID
- Pastikan struktur array sesuai format

### URL tidak bekerja
- Cek routing FilamentPHP
- Pastikan URL sesuai dengan panel path
