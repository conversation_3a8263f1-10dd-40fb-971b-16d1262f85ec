<?php

// Test file untuk RajaMenuPlugin
// Copy kode ini ke AdminPanelProvider untuk test

use <PERSON><PERSON>les\RajaMenu\Plugins\RajaMenuPlugin;

// ===== CARA BARU (RECOMMENDED) - AUTO RENDER =====

// Contoh 1: Mode Auto dengan Auto-Render (Default)
RajaMenuPlugin::make()
    ->enabled(true)
    ->auto()
    ->panel('admin');
    // autoRender(true) adalah default - navigasi muncul otomatis!

// Contoh 2: Mode Auto dengan Manual Render (Jika perlu kontrol)
RajaMenuPlugin::make()
    ->enabled(true)
    ->auto()
    ->panel('admin')
    ->manualRender(); // Nonaktifkan auto-render

// Contoh 3: Mode Manual Simple
RajaMenuPlugin::make()
    ->enabled(true)
    ->manual()
    ->panel('admin')
    ->dashboard()
    ->contentManagement()
    ->userManagement()
    ->systemSettings();

// Contoh 4: Mode Manual Custom
RajaMenuPlugin::make()
    ->enabled(true)
    ->manual()
    ->panel('admin')
    ->addGroup('Dashboard', [
        'label' => 'Dashboard',
        'icon' => 'heroicon-o-home',
        'sort' => -10,
        'items' => [
            [
                'label' => 'Admin Dashboard',
                'icon' => 'heroicon-o-squares-2x2',
                'url' => '/admin',
                'sort' => 1,
                'badge' => null,
                'type' => 'page',
            ],
        ],
    ])
    ->addGroup('Test', [
        'label' => 'Test Menu',
        'icon' => 'heroicon-o-beaker',
        'sort' => -5,
        'items' => [
            [
                'label' => 'Test Item 1',
                'icon' => 'heroicon-o-star',
                'url' => '/admin/test1',
                'sort' => 1,
                'badge' => 'New',
                'type' => 'page',
            ],
            [
                'label' => 'Test Parent',
                'icon' => 'heroicon-o-folder',
                'url' => '#',
                'sort' => 2,
                'type' => 'parent',
                'children' => [
                    [
                        'label' => 'Test Child 1',
                        'icon' => 'heroicon-o-document',
                        'url' => '/admin/test/child1',
                        'sort' => 1,
                        'type' => 'page',
                    ],
                    [
                        'label' => 'Test Child 2',
                        'icon' => 'heroicon-o-document-text',
                        'url' => '/admin/test/child2',
                        'sort' => 2,
                        'badge' => '5',
                        'type' => 'page',
                    ],
                ],
            ],
        ],
    ]);

// Contoh 5: Multiple Panels
// Panel Admin
RajaMenuPlugin::make()
    ->enabled(true)
    ->auto()
    ->panel('admin');

// Panel Marketplace
RajaMenuPlugin::make()
    ->enabled(true)
    ->manual()
    ->panel('marketplace')
    ->addGroup('Dashboard', [
        'label' => 'Marketplace',
        'icon' => 'heroicon-o-building-storefront',
        'sort' => -10,
        'items' => [
            [
                'label' => 'Seller Dashboard',
                'icon' => 'heroicon-o-chart-bar',
                'url' => '/marketplace',
                'sort' => 1,
                'type' => 'page',
            ],
        ],
    ])
    ->addGroup('Products', [
        'label' => 'My Products',
        'icon' => 'heroicon-o-cube',
        'sort' => -5,
        'items' => [
            [
                'label' => 'Product List',
                'icon' => 'heroicon-o-list-bullet',
                'url' => '/marketplace/products',
                'sort' => 1,
                'badge' => '25',
                'type' => 'resource',
            ],
            [
                'label' => 'Add Product',
                'icon' => 'heroicon-o-plus-circle',
                'url' => '/marketplace/products/create',
                'sort' => 2,
                'type' => 'page',
            ],
        ],
    ]);

// Panel Member
RajaMenuPlugin::make()
    ->enabled(true)
    ->manual()
    ->panel('member')
    ->addGroup('Dashboard', [
        'label' => 'Member Area',
        'icon' => 'heroicon-o-user',
        'sort' => -10,
        'items' => [
            [
                'label' => 'My Dashboard',
                'icon' => 'heroicon-o-home',
                'url' => '/member',
                'sort' => 1,
                'type' => 'page',
            ],
        ],
    ])
    ->addGroup('Profile', [
        'label' => 'My Profile',
        'icon' => 'heroicon-o-user-circle',
        'sort' => -5,
        'items' => [
            [
                'label' => 'Profile Info',
                'icon' => 'heroicon-o-identification',
                'url' => '/member/profile',
                'sort' => 1,
                'type' => 'page',
            ],
            [
                'label' => 'Settings',
                'icon' => 'heroicon-o-cog-6-tooth',
                'url' => '/member/settings',
                'sort' => 2,
                'type' => 'page',
            ],
        ],
    ]);

// Contoh 6: Conditional Plugin
RajaMenuPlugin::make()
    ->enabled(auth()->user()?->hasRole('admin')) // Hanya untuk admin
    ->auto()
    ->panel('admin');

// Contoh 7: Dynamic Badge
RajaMenuPlugin::make()
    ->enabled(true)
    ->manual()
    ->panel('admin')
    ->addGroup('Orders', [
        'label' => 'Orders',
        'icon' => 'heroicon-o-shopping-bag',
        'sort' => 0,
        'items' => [
            [
                'label' => 'Pending Orders',
                'icon' => 'heroicon-o-clock',
                'url' => '/admin/orders/pending',
                'sort' => 1,
                'badge' => \App\Models\Order::where('status', 'pending')->count(),
                'type' => 'page',
            ],
        ],
    ]);
