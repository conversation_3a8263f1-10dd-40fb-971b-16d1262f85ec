<?php

namespace Modules\RajaMenu\Plugins;

use Filament\Contracts\Plugin;
use Filament\Panel;

class RajaMenuPlugin implements Plugin
{
    protected bool $enabled = true;
    protected bool $autoRender = true; // Auto-render navigation
    protected string $mode = 'auto'; // 'auto' or 'manual'
    protected array $manualNavigation = [];
    protected string $panelId = 'admin';
    protected array $autoDiscoveryConfig = [];

    public static function make(): static
    {
        return app(static::class);
    }

    public function getId(): string
    {
        return 'rajamenu';
    }

    /**
     * Enable or disable RajaMenu
     */
    public function enabled(bool $enabled = true): static
    {
        $this->enabled = $enabled;
        return $this;
    }

    /**
     * Enable or disable auto-render navigation
     */
    public function autoRender(bool $autoRender = true): static
    {
        $this->autoRender = $autoRender;
        return $this;
    }

    /**
     * Disable auto-render (for manual render hook usage)
     */
    public function manualRender(): static
    {
        $this->autoRender = false;
        return $this;
    }

    /**
     * Set navigation mode
     */
    public function mode(string $mode): static
    {
        $this->mode = $mode;
        return $this;
    }

    /**
     * Use auto-discovery mode
     */
    public function auto(): static
    {
        $this->mode = 'auto';
        return $this;
    }

    /**
     * Use manual mode with navigation items
     */
    public function manual(array $navigationItems = []): static
    {
        $this->mode = 'manual';
        $this->manualNavigation = $navigationItems;
        return $this;
    }

    /**
     * Set panel ID
     */
    public function panel(string $panelId): static
    {
        $this->panelId = $panelId;
        return $this;
    }

    /**
     * Configure auto-discovery settings
     */
    public function autoDiscovery(array $config): static
    {
        $this->autoDiscoveryConfig = $config;
        return $this;
    }

    /**
     * Add navigation group for manual mode
     */
    public function addGroup(string $groupName, array $groupConfig): static
    {
        $this->manualNavigation[$groupName] = $groupConfig;
        return $this;
    }

    /**
     * Add dashboard group
     */
    public function dashboard(array $config = []): static
    {
        $defaultConfig = [
            'label' => 'Dashboard',
            'icon' => 'heroicon-o-home',
            'sort' => -10,
            'items' => [
                [
                    'label' => 'Dashboard',
                    'icon' => 'heroicon-o-squares-2x2',
                    'url' => "/{$this->panelId}",
                    'sort' => 1,
                    'badge' => null,
                    'type' => 'page',
                ],
            ],
        ];

        $this->manualNavigation['Dashboard'] = array_merge($defaultConfig, $config);
        return $this;
    }

    /**
     * Add content management group
     */
    public function contentManagement(array $config = []): static
    {
        $defaultConfig = [
            'label' => 'Content',
            'icon' => 'heroicon-o-document-text',
            'sort' => -5,
            'items' => [
                [
                    'label' => 'Content Management',
                    'icon' => 'heroicon-o-book-open',
                    'url' => '#',
                    'sort' => 1,
                    'type' => 'parent',
                    'children' => [
                        [
                            'label' => 'Articles',
                            'icon' => 'heroicon-o-newspaper',
                            'url' => "/{$this->panelId}/articles",
                            'sort' => 1,
                            'type' => 'resource',
                        ],
                        [
                            'label' => 'Pages',
                            'icon' => 'heroicon-o-document',
                            'url' => "/{$this->panelId}/pages",
                            'sort' => 2,
                            'type' => 'resource',
                        ],
                    ],
                ],
                [
                    'label' => 'Media Library',
                    'icon' => 'heroicon-o-photo',
                    'url' => "/{$this->panelId}/media",
                    'sort' => 2,
                    'type' => 'resource',
                ],
            ],
        ];

        $this->manualNavigation['Content'] = array_merge($defaultConfig, $config);
        return $this;
    }

    /**
     * Add user management group
     */
    public function userManagement(array $config = []): static
    {
        $defaultConfig = [
            'label' => 'Users',
            'icon' => 'heroicon-o-users',
            'sort' => 0,
            'items' => [
                [
                    'label' => 'Users',
                    'icon' => 'heroicon-o-user-group',
                    'url' => "/{$this->panelId}/users",
                    'sort' => 1,
                    'type' => 'resource',
                ],
                [
                    'label' => 'Roles & Permissions',
                    'icon' => 'heroicon-o-shield-check',
                    'url' => '#',
                    'sort' => 2,
                    'type' => 'parent',
                    'children' => [
                        [
                            'label' => 'Roles',
                            'icon' => 'heroicon-o-identification',
                            'url' => "/{$this->panelId}/roles",
                            'sort' => 1,
                            'type' => 'resource',
                        ],
                        [
                            'label' => 'Permissions',
                            'icon' => 'heroicon-o-key',
                            'url' => "/{$this->panelId}/permissions",
                            'sort' => 2,
                            'type' => 'resource',
                        ],
                    ],
                ],
            ],
        ];

        $this->manualNavigation['Users'] = array_merge($defaultConfig, $config);
        return $this;
    }

    /**
     * Add system settings group
     */
    public function systemSettings(array $config = []): static
    {
        $defaultConfig = [
            'label' => 'System',
            'icon' => 'heroicon-o-cog-6-tooth',
            'sort' => 10,
            'items' => [
                [
                    'label' => 'Settings',
                    'icon' => 'heroicon-o-wrench-screwdriver',
                    'url' => '#',
                    'sort' => 1,
                    'type' => 'parent',
                    'children' => [
                        [
                            'label' => 'General Settings',
                            'icon' => 'heroicon-o-cog-8-tooth',
                            'url' => "/{$this->panelId}/settings/general",
                            'sort' => 1,
                            'type' => 'page',
                        ],
                        [
                            'label' => 'Email Settings',
                            'icon' => 'heroicon-o-envelope',
                            'url' => "/{$this->panelId}/settings/email",
                            'sort' => 2,
                            'type' => 'page',
                        ],
                    ],
                ],
                [
                    'label' => 'Logs',
                    'icon' => 'heroicon-o-document-text',
                    'url' => "/{$this->panelId}/logs",
                    'sort' => 2,
                    'type' => 'page',
                ],
            ],
        ];

        $this->manualNavigation['System'] = array_merge($defaultConfig, $config);
        return $this;
    }

    /**
     * Register the plugin
     */
    public function register(Panel $panel): void
    {
        if (!$this->enabled) {
            return;
        }

        // Set panel ID from panel
        $this->panelId = $panel->getId();

        // Register navigation configuration for this panel
        $this->registerNavigationConfig();

        // Register views and assets
        $this->registerViews($panel);
    }

    /**
     * Boot the plugin
     */
    public function boot(Panel $panel): void
    {
        if (!$this->enabled) {
            return;
        }

        // Additional boot logic if needed
    }

    /**
     * Register navigation configuration
     */
    protected function registerNavigationConfig(): void
    {
        // Set runtime configuration for this panel
        config([
            "rajamenu.navigation_mode" => $this->mode,
            "rajamenu.manual_navigation.{$this->panelId}.groups" => $this->manualNavigation,
        ]);

        if (!empty($this->autoDiscoveryConfig)) {
            config([
                "rajamenu.auto_discovery" => array_merge(
                    config('rajamenu.auto_discovery', []),
                    $this->autoDiscoveryConfig
                )
            ]);
        }
    }

    /**
     * Register views and assets
     */
    protected function registerViews(Panel $panel): void
    {
        // Register render hook hanya jika autoRender diaktifkan
        if ($this->autoRender) {
            $panel->renderHook(
                'panels::global-search.before',
                function() {
                    try {
                        return view('rajamenu::navigation.auto-flyout')->render();
                    } catch (\Exception $e) {
                        return '<div style="background: red; color: white; padding: 10px; margin: 10px 0; border-radius: 6px;">RajaMenu Error: ' . $e->getMessage() . '</div>';
                    }
                }
            );
        }
    }
}
