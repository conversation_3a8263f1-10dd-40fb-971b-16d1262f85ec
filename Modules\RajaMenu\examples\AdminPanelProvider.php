<?php

namespace App\Providers\Filament;

use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Modules\RajaMenu\Plugins\RajaMenuPlugin;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->login()
            ->colors([
                'primary' => Color::Amber,
            ])
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,
                Widgets\FilamentInfoWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            // ===== RAJAMENU PLUGIN CONFIGURATION =====
            ->plugins([
                // Contoh 1: Mode Auto dengan Auto-Render (Recommended)
                RajaMenuPlugin::make()
                    ->enabled(true)
                    ->auto()
                    ->panel('admin'),
                    // autoRender(true) adalah default - navigasi muncul otomatis

                // Contoh 2: Mode Auto dengan Manual Render
                // RajaMenuPlugin::make()
                //     ->enabled(true)
                //     ->auto()
                //     ->panel('admin')
                //     ->manualRender(), // Nonaktifkan auto-render

                // Contoh 3: Mode Manual dengan helper methods
                // RajaMenuPlugin::make()
                //     ->enabled(true)
                //     ->manual()
                //     ->panel('admin')
                //     ->dashboard()
                //     ->contentManagement()
                //     ->userManagement()
                //     ->systemSettings(),

                // Contoh 3: Mode Manual dengan konfigurasi custom
                // RajaMenuPlugin::make()
                //     ->enabled(true)
                //     ->manual()
                //     ->panel('admin')
                //     ->addGroup('Dashboard', [
                //         'label' => 'Dashboard',
                //         'icon' => 'heroicon-o-home',
                //         'sort' => -10,
                //         'items' => [
                //             [
                //                 'label' => 'Admin Dashboard',
                //                 'icon' => 'heroicon-o-squares-2x2',
                //                 'url' => '/admin',
                //                 'sort' => 1,
                //                 'badge' => null,
                //                 'type' => 'page',
                //             ],
                //         ],
                //     ])
                //     ->addGroup('Content', [
                //         'label' => 'Content Management',
                //         'icon' => 'heroicon-o-document-text',
                //         'sort' => -5,
                //         'items' => [
                //             [
                //                 'label' => 'Articles',
                //                 'icon' => 'heroicon-o-newspaper',
                //                 'url' => '/admin/articles',
                //                 'sort' => 1,
                //                 'badge' => '25',
                //                 'type' => 'resource',
                //             ],
                //             [
                //                 'label' => 'Media Library',
                //                 'icon' => 'heroicon-o-photo',
                //                 'url' => '/admin/media',
                //                 'sort' => 2,
                //                 'badge' => '156',
                //                 'type' => 'resource',
                //             ],
                //         ],
                //     ])
                //     ->addGroup('System', [
                //         'label' => 'System',
                //         'icon' => 'heroicon-o-cog-6-tooth',
                //         'sort' => 10,
                //         'items' => [
                //             [
                //                 'label' => 'Users',
                //                 'icon' => 'heroicon-o-users',
                //                 'url' => '/admin/users',
                //                 'sort' => 1,
                //                 'badge' => '89',
                //                 'type' => 'resource',
                //             ],
                //             [
                //                 'label' => 'Settings',
                //                 'icon' => 'heroicon-o-wrench-screwdriver',
                //                 'url' => '#',
                //                 'sort' => 2,
                //                 'type' => 'parent',
                //                 'children' => [
                //                     [
                //                         'label' => 'General Settings',
                //                         'icon' => 'heroicon-o-cog-8-tooth',
                //                         'url' => '/admin/settings/general',
                //                         'sort' => 1,
                //                         'type' => 'page',
                //                     ],
                //                     [
                //                         'label' => 'Email Settings',
                //                         'icon' => 'heroicon-o-envelope',
                //                         'url' => '/admin/settings/email',
                //                         'sort' => 2,
                //                         'type' => 'page',
                //                     ],
                //                 ],
                //             ],
                //         ],
                //     ]),
            ]);
    }
}
